#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration loader for UFN Server
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from logging.handlers import RotatingFileHandler


class ConfigLoader:
    """Configuration loader and manager"""
    
    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            if not os.path.exists(self.config_file):
                raise FileNotFoundError(f"Configuration file {self.config_file} not found")
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # Validate required sections
            required_sections = ['server', 'logging', 'app']
            for section in required_sections:
                if section not in config:
                    raise ValueError(f"Missing required configuration section: {section}")
            
            return config
        
        except Exception as e:
            print(f"Error loading configuration: {e}")
            # Return default configuration
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'server': {
                'host': '0.0.0.0',
                'port': 8080,
                'timeout': 30,
                'max_connections': 100,
                'listen_backlog': 100
            },
            'grpc': {
                'server_url': None
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': {
                    'enabled': True,
                    'directory': 'logs',
                    'filename': 'ufn_server.log',
                    'max_size_mb': 10,
                    'backup_count': 5
                },
                'console': {
                    'enabled': True,
                    'level': 'INFO'
                }
            },
            'app': {
                'name': 'UFN Protocol Server',
                'version': '1.0.0',
                'description': 'UFN Protocol Server for firmware upgrades'
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value by key (supports dot notation)"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_server_config(self) -> Dict[str, Any]:
        """Get server configuration"""
        return self.config.get('server', {})
    
    def get_grpc_config(self) -> Dict[str, Any]:
        """Get gRPC configuration"""
        return self.config.get('grpc', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return self.config.get('logging', {})
    
    def get_app_config(self) -> Dict[str, Any]:
        """Get application configuration"""
        return self.config.get('app', {})
    
    def setup_logging(self) -> None:
        """Setup logging based on configuration"""
        log_config = self.get_logging_config()
        
        # Create logs directory if it doesn't exist
        if log_config.get('file', {}).get('enabled', True):
            log_dir = log_config.get('file', {}).get('directory', 'logs')
            os.makedirs(log_dir, exist_ok=True)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_config.get('level', 'INFO')))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Setup formatter
        formatter = logging.Formatter(
            log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        )
        
        # Setup file handler
        if log_config.get('file', {}).get('enabled', True):
            log_dir = log_config.get('file', {}).get('directory', 'logs')
            log_file = log_config.get('file', {}).get('filename', 'ufn_server.log')
            max_size = log_config.get('file', {}).get('max_size_mb', 10) * 1024 * 1024
            backup_count = log_config.get('file', {}).get('backup_count', 5)
            
            file_handler = RotatingFileHandler(
                os.path.join(log_dir, log_file),
                maxBytes=max_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        
        # Setup console handler
        if log_config.get('console', {}).get('enabled', True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            console_level = log_config.get('console', {}).get('level', 'INFO')
            console_handler.setLevel(getattr(logging, console_level))
            root_logger.addHandler(console_handler)


# Global configuration instance
config = ConfigLoader()
