# UFN Server Configuration
server:
  host: "0.0.0.0"
  port: 8080
  timeout: 30  # Client connection timeout in seconds
  max_connections: 100  # Maximum concurrent connections
  listen_backlog: 100  # Socket listen backlog

# gRPC Configuration
grpc:
  server_url: null  # gRPC server URL, set to null to disable

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # File logging
  file:
    enabled: true
    directory: "logs"
    filename: "ufn_server.log"
    max_size_mb: 10  # Maximum log file size in MB
    backup_count: 5  # Number of backup files to keep
  
  # Console logging
  console:
    enabled: true
    level: "INFO"

# Application Settings
app:
  name: "UFN Protocol Server"
  version: "1.0.0"
  description: "UFN (Upgrade From NET) Protocol Server for firmware upgrades"
