#!/bin/bash

# UFN Protocol Server Startup Script
# This script starts the UFN Protocol Server with proper environment setup

set -e  # Exit on any error

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Configuration
PYTHON_CMD="python3"
MAIN_SCRIPT="main.py"
CONFIG_FILE="config.yaml"
PID_FILE="ufn_server.pid"
LOG_DIR="logs"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if server is running
is_server_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# Function to start the server
start_server() {
    print_status "Starting UFN Protocol Server..."
    
    # Check if already running
    if is_server_running; then
        print_warning "Server is already running (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    # Check Python installation
    if ! command -v $PYTHON_CMD &> /dev/null; then
        print_error "Python3 is not installed or not in PATH"
        return 1
    fi
    
    # Check main script exists
    if [ ! -f "$MAIN_SCRIPT" ]; then
        print_error "Main script $MAIN_SCRIPT not found"
        return 1
    fi
    
    # Check configuration file
    if [ ! -f "$CONFIG_FILE" ]; then
        print_warning "Configuration file $CONFIG_FILE not found, using defaults"
    fi
    
    # Create logs directory
    mkdir -p "$LOG_DIR"
    
    # Check and install dependencies
    if [ -f "requirements.txt" ]; then
        print_status "Checking dependencies..."
        $PYTHON_CMD -m pip install -r requirements.txt --quiet
    fi
    
    # Start the server
    print_status "Launching server..."
    nohup $PYTHON_CMD "$MAIN_SCRIPT" > "$LOG_DIR/startup.log" 2>&1 &
    local server_pid=$!
    
    # Save PID
    echo $server_pid > "$PID_FILE"
    
    # Wait a moment and check if server started successfully
    sleep 2
    if ps -p $server_pid > /dev/null 2>&1; then
        print_success "UFN Protocol Server started successfully (PID: $server_pid)"
        print_status "Server logs: $LOG_DIR/ufn_server.log"
        print_status "Startup logs: $LOG_DIR/startup.log"
        return 0
    else
        print_error "Failed to start server. Check $LOG_DIR/startup.log for details"
        rm -f "$PID_FILE"
        return 1
    fi
}

# Function to stop the server
stop_server() {
    print_status "Stopping UFN Protocol Server..."
    
    if ! is_server_running; then
        print_warning "Server is not running"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    kill $pid
    
    # Wait for graceful shutdown
    local count=0
    while ps -p $pid > /dev/null 2>&1 && [ $count -lt 10 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    # Force kill if still running
    if ps -p $pid > /dev/null 2>&1; then
        print_warning "Forcing server shutdown..."
        kill -9 $pid
    fi
    
    rm -f "$PID_FILE"
    print_success "UFN Protocol Server stopped"
}

# Function to restart the server
restart_server() {
    stop_server
    sleep 2
    start_server
}

# Function to show server status
show_status() {
    if is_server_running; then
        local pid=$(cat "$PID_FILE")
        print_success "UFN Protocol Server is running (PID: $pid)"
        
        # Show some process info
        echo "Process details:"
        ps -p $pid -o pid,ppid,cmd,etime,pcpu,pmem
        
        # Show recent logs
        if [ -f "$LOG_DIR/ufn_server.log" ]; then
            echo ""
            echo "Recent log entries:"
            tail -n 5 "$LOG_DIR/ufn_server.log"
        fi
    else
        print_warning "UFN Protocol Server is not running"
    fi
}

# Function to show logs
show_logs() {
    if [ -f "$LOG_DIR/ufn_server.log" ]; then
        tail -f "$LOG_DIR/ufn_server.log"
    else
        print_error "Log file not found: $LOG_DIR/ufn_server.log"
    fi
}

# Main script logic
case "${1:-start}" in
    start)
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        restart_server
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the UFN Protocol Server"
        echo "  stop    - Stop the UFN Protocol Server"
        echo "  restart - Restart the UFN Protocol Server"
        echo "  status  - Show server status"
        echo "  logs    - Show and follow server logs"
        exit 1
        ;;
esac
