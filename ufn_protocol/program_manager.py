#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
程序数据管理器
"""

import logging
from typing import Dict, Optional
from .grpc_client import GRPCClient

logger = logging.getLogger(__name__)


class ProgramManager:
    """程序数据管理器"""
    
    def __init__(self, grpc_client: Optional[GRPCClient] = None):
        # 修改数据结构为 {(program_id, version): program_data}
        self.program_data: Dict[tuple, Dict] = {}
        self.default_chunk_size = 1024
        self.grpc_client = grpc_client
    
    def load_program_from_grpc(self, program_id: str, file_data: bytes, version: int,
                              area: int = 0, option: int = 0, mark1: int = 0, mark2: int = 0,
                              file_url: str = None):
        """加载程序数据（直接使用文件字节数组）

        Args:
            program_id: 程序ID
            file_data: 文件字节数组（从 query_upgrade_info 直接获取）
            version: 版本号
            area: 区域
            option: 选项
            mark1: 标记1
            mark2: 标记2
            file_url: 文件地址（可选，仅用于日志记录）
        """
        try:
            program_id_str = program_id.hex() if isinstance(program_id, bytes) else str(program_id)
            logger.info(f"Loading program {program_id_str} v{version} ({len(file_data)} bytes)")

            if not file_data:
                logger.error(f"No file data provided for program {program_id}")
                return False
            
            # 组装程序信息（64字节）
            program_info = self._build_program_info(
                version=version,
                program_id=program_id,
                program_data=file_data,
                area=area,
                option=option,
                mark1=mark1,
                mark2=mark2
            )

            # 添加到程序数据中，使用(program_id, version)作为键
            key = (program_id, version)
            self.program_data[key] = {
                'version': version,
                'program_id': program_id,
                'data': file_data,
                'info': program_info,
                'size': len(file_data),
                'area': area,
                'option': option,
                'mark1': mark1,
                'mark2': mark2
            }

            logger.info(f"Successfully loaded program {program_id_str} v{version} ({len(file_data)} bytes)")
            return True
        except Exception as e:
            logger.error(f"Failed to load program: {e}")
            return False

    def _build_program_info(self, version: int, program_id: str, program_data: bytes,
                           area: int, option: int, mark1: int, mark2: int) -> bytes:
        """组装程序信息格式（64字节）"""
        import struct
        from datetime import datetime
        
        try:
            # 获取当前时间
            now = datetime.now()
            
            # 计算程序累加和
            proc_sum = sum(program_data) & 0xFFFFFFFF
            
            # 构建64字节程序信息
            info = bytearray(64)
            
            # U16 ver; 版本号
            struct.pack_into('>H', info, 0, version)
            
            # BYTE year,month,day,hour,minute,seconds; 年月日时分秒
            struct.pack_into('6B', info, 2, 
                now.year % 100, now.month, now.day,
                now.hour, now.minute, now.second)
            
            # BYTE RSV1[8]; 预留字段1 - 填充0xFF
            info[8:16] = b'\xFF' * 8
            
            # U32 proc_length; 程序长度
            struct.pack_into('<I', info, 16, len(program_data))
            
            # U32 proc_sum; 程序累加和
            struct.pack_into('<I', info, 20, proc_sum)
            
            # char proc_id[8]; 程序ID - 固定为程序ID，不足8字节用0x00填充
            if isinstance(program_id, bytes):
                proc_id_bytes = program_id[:8].ljust(8, b'\x00')
            else:
                proc_id_bytes = program_id.encode('ascii')[:8].ljust(8, b'\x00')
            info[24:32] = proc_id_bytes
            
            # char Area; 区域选项
            info[32] = area
            
            # char Option; 附加选项
            info[33] = option
            
            # char Mark1,Mark2; 标识1，标识2
            info[34] = mark1
            info[35] = mark2
            
            # char desc[24]; 程序描述 - 没有信息用0xFF填充
            info[36:60] = b'\xFF' * 24
            
            # BYTE RSV2[4]; 预留字段2 - 填充0xFF
            info[60:64] = b'\xFF' * 4
            
            return bytes(info)
            
        except Exception as e:
            logger.error(f"Failed to build program info: {e}")
            return b'\xFF' * 64  # 失败时返回全0xFF

    def get_program_data(self) -> Dict[tuple, Dict]:
        """获取所有程序数据"""
        return self.program_data
    
    def has_program(self, program_id: str, version: int = None) -> bool:
        """
        检查是否存在指定程序
        
        Args:
            program_id: 程序ID
            version: 版本号，如果为None则只检查program_id
        """
        if version is None:
            # 只检查program_id
            return any(pid == program_id for (pid, _) in self.program_data.keys())
        else:
            # 同时检查program_id和version
            return (program_id, version) in self.program_data
    
    def get_program(self, program_id: str, version: int = None) -> Optional[Dict]:
        """
        获取指定程序数据
        
        Args:
            program_id: 程序ID
            version: 版本号，如果为None则返回该program_id的最新版本
        """
        if version is not None:
            # 如果指定了版本，直接返回
            key = (program_id, version)
            return self.program_data.get(key)
        else:
            # 如果没有指定版本，返回该program_id的最新版本
            matching_programs = [(pid, ver) for (pid, ver) in self.program_data.keys() if pid == program_id]
            if not matching_programs:
                return None
            
            # 按版本号排序，取最高版本
            latest_key = max(matching_programs, key=lambda x: x[1])
            return self.program_data.get(latest_key)
