#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UFN服务器主程序
"""

import logging
from ufn_protocol import UFNServer #, AWSS3Config, OTAUpgrade

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ufn_server.log'),  # 输出到文件
        logging.StreamHandler()  # 同时输出到控制台
    ]
)
logger = logging.getLogger(__name__)


def main():
    """主函数示例"""
    
    # # 配置 AWS S3（非必需）
    # aws_s3_config = None
    # try:
    #     # 从配置文件加载 AWS S3 配置
    #     aws_s3_config = AWSS3Config.from_file('Resource/aws_s3_config')
    #     logger.info("AWS S3 configuration loaded")
    # except Exception as e:
    #     logger.error(f"Failed to load AWS S3 config: {e}")
    #     logger.error("AWS S3 configuration is required for this server")
    #     return
    
    # 创建服务器实例
    server = UFNServer(host='0.0.0.0', port=8080)
    
    # 从 AWS S3 加载程序
    # s3_url = "https://eplvs.s3-accelerate.amazonaws.com/ota/4c4e96cf-9937-4299-b113-5948bb7f2e53_ccu-10045_NT-B2G-V3 2024.7.10_Ex.yxp"
    # server.load_program_from_s3(
    #     program_id='fedcba0987654321',
    #     s3_url=s3_url,
    #     version=10300
    # )
    
    # # 模拟 OTA 升级场景
    # ota_upgrade = OTAUpgrade(
    #     vin="2023233206001",
    #     upgrade_type=0,  # CCU 升级
    #     platform_version="10400",
    #     after_change_url=s3_url
    # )
    # server.load_ota_upgrade(ota_upgrade, 'ota_upgrade_001')
    
    try:
        logger.info("Starting UFN server...")
        server.start()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        server.stop()


if __name__ == '__main__':
    main()
