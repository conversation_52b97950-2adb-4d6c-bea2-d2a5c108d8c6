#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UFN服务器主程序
"""

import logging
import signal
import sys
from ufn_protocol import UFNServer
from config_loader import config

logger = logging.getLogger(__name__)


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"Received signal {signum}, shutting down gracefully...")
    sys.exit(0)


def main():
    """主函数"""
    try:
        # 设置日志
        config.setup_logging()

        # 获取应用配置
        app_config = config.get_app_config()
        server_config = config.get_server_config()
        grpc_config = config.get_grpc_config()

        logger.info(f"Starting {app_config.get('name', 'UFN Server')} v{app_config.get('version', '1.0.0')}")
        logger.info(f"Description: {app_config.get('description', 'UFN Protocol Server')}")

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # 创建服务器实例
        server = UFNServer(
            host=server_config.get('host', '0.0.0.0'),
            port=server_config.get('port', 8080),
            timeout=server_config.get('timeout', 30),
            max_connections=server_config.get('max_connections', 100),
            listen_backlog=server_config.get('listen_backlog', 100),
            grpc_server_url=grpc_config.get('server_url')
        )

        logger.info(f"Server configuration:")
        logger.info(f"  Host: {server_config.get('host', '0.0.0.0')}")
        logger.info(f"  Port: {server_config.get('port', 8080)}")
        logger.info(f"  Timeout: {server_config.get('timeout', 30)}s")
        logger.info(f"  Max connections: {server_config.get('max_connections', 100)}")
        logger.info(f"  Listen backlog: {server_config.get('listen_backlog', 100)}")
        logger.info(f"  gRPC URL: {grpc_config.get('server_url', 'Not configured')}")

        # 启动服务器
        server.start()

    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
